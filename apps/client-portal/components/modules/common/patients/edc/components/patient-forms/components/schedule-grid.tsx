import { Diff } from "lucide-react";
import React from "react";

import { StatusComponent } from "@/components/ui/badge/edc/progress-indicator";
import { Tooltip } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import {
  FormPeriod,
  Forms,
  formVisitProgresses,
} from "../../patient-dashboard/data";

interface PeriodConfig {
  title: string;
  visits: Array<{
    id: string;
    name: string;
    subName: string;
    visitDay: number;
    visitWindow: number;
    period: FormPeriod;
    forms: Forms[];
  }>;
}

interface ScheduleGridProps {
  periodConfig: PeriodConfig[];
  filteredForms: Forms[];
}

const tooltipMessages = {
  notRequired: "No Form Required",
  notStarted: "Not Started",
  inProgress: "In Progress",
  readyForSdv: "Ready for SDV",
  sourceDataVerified: "Source Data Verified",
  completed: "Form Completed",
  containOpenQuery: "Has Open Queries",
};

export const ScheduleGrid: React.FC<ScheduleGridProps> = ({
  periodConfig,
  filteredForms,
}) => {
  return (
    <div
      className="grid flex-1 overflow-x-auto rounded-lg border"
      style={{
        gridTemplateColumns: `auto ${periodConfig.map((config) => `repeat(${config.visits.length}, 1fr)`).join(" ")}`,
        gridTemplateRows: `auto auto repeat(${filteredForms.length}, 1fr)`,
      }}
    >
      {/* Form Column Header */}
      <div className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]">
        Visit / Activity
      </div>

      {/* Period Column Headers */}
      {periodConfig.map((config) => (
        <div
          key={config.title}
          className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]"
          style={{ gridColumn: `span ${config.visits.length}` }}
        >
          {config.title}
        </div>
      ))}

      {/* Visit Windows / Days row header */}
      <div className="whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]">
        Visit Windows / Days
      </div>

      {/* Visit headers for each period */}
      {periodConfig.map((config) =>
        config.visits.map((visit) => (
          <div
            key={`header-${visit.id}`}
            className="flex items-center whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]"
          >
            {visit.name} / Day {visit.visitDay} <Diff size={12} />{" "}
            {visit.visitWindow}d
          </div>
        )),
      )}

      {/* Form rows */}
      {filteredForms.map((form) => (
        <React.Fragment key={`form-${form.id}`}>
          {/* Form name column */}
          <div
            key={`form-name-${form.id}`}
            className="whitespace-nowrap border border-gray-100 px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500"
          >
            {form.name}
          </div>

          {/* Form cells for each visit */}
          {periodConfig.map((config) =>
            config.visits.map((visit) => {
              const formVisit = config.visits.find(
                (visit2) =>
                  visit.forms.some((formVisit) => formVisit.id === form.id) &&
                  visit2.id === visit.id,
              );

              const formVisitProgress = formVisitProgresses.find(
                (formVisitProgress) =>
                  formVisitProgress.formVisitId === visit.id &&
                  formVisitProgress.id === form.id,
              );

              const notRequiredForm = !formVisit;

              return (
                <div
                  key={`${form.id}-${visit.id}`}
                  className={cn(
                    "relative whitespace-nowrap border border-[#EAECF0] px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500",
                    notRequiredForm && "bg-[#F9FAFB]",
                  )}
                >
                  <Tooltip
                    content={
                      tooltipMessages[
                        formVisitProgress?.variant ?? "notRequired"
                      ]
                    }
                    variant="primary"
                    theme={{
                      target: "w-ful",
                    }}
                  >
                    <span className="invisible">X</span>
                    <div
                      className={cn(
                        "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",
                        notRequiredForm && "hidden",
                      )}
                    >
                      <StatusComponent
                        percentage={formVisitProgress?.progress ?? 0}
                        size={16}
                        strokeWidth={2}
                        variant={formVisitProgress?.variant ?? "notStarted"}
                      />
                    </div>
                  </Tooltip>
                </div>
              );
            }),
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
