import { Card } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import React, { useMemo, useState } from "react";

import { FormPeriod, forms, formVisits } from "../patient-dashboard/data";
import {
  ColumnVisibilityDropdown,
  FormStatusFilters,
  ScheduleGrid,
} from "./components";

type FilterStatus =
  | "notStarted"
  | "inProgress"
  | "activeQueries"
  | "readyForSdv"
  | "sourceDataVerified"
  | "formSigned"
  | "locked"
  | "archived";

export const PatientForm = () => {
  const [filterStatus, setFilterStatus] = useQueryState(
    "filterStatus",
    parseAsString.withDefault(""),
  );

  const [selectedVisits, setSelectedVisits] = useState<Set<string>>(
    () => new Set(formVisits.map((visit) => visit.id)),
  );

  const periodConfig = useMemo(() => {
    const config: Array<{ period: FormPeriod; title: string }> = [
      { period: "screening", title: "Screening" },
      { period: "treatment", title: "Treatment" },
      { period: "followUp", title: "Follow-up" },
      { period: "etd", title: "End of Treatment" },
    ];

    return config
      .map(({ period, title }) => ({
        title,
        visits: formVisits.filter((visit) => {
          const isPeriodMatch = visit.period === period;
          const isVisitSelected = selectedVisits.has(visit.id);
          return isPeriodMatch && isVisitSelected;
        }),
      }))
      .filter((config) => config.visits.length > 0); // Only include periods that have visible visits
  }, [selectedVisits]);

  const filteredForms = useMemo(() => {
    if (!filterStatus || filterStatus === "") {
      return forms;
    }

    return forms.filter((form) => form.status === filterStatus);
  }, [filterStatus]);

  const handleFilterClick = (status: FilterStatus) => {
    // If clicking the currently selected status, reset to show all
    if (filterStatus === status) {
      setFilterStatus("");
    } else {
      // Otherwise, set the new filter
      setFilterStatus(status);
    }
  };

  const handleVisitSelection = (visitId: string, checked: boolean) => {
    setSelectedVisits((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(visitId);
      } else {
        newSet.delete(visitId);
      }
      return newSet;
    });
  };

  const handleShowAllVisits = () => {
    setSelectedVisits(new Set(formVisits.map((visit) => visit.id)));
  };

  return (
    <Card className="h-full flex-1 rounded-xl">
      <div className="flex h-full flex-col gap-5 p-6">
        <div className="h-full rounded-lg border p-6">
          {/* Header and filters */}
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <h2 className="text-xl font-semibold leading-5">
                Schedule of Activities
              </h2>

              <ColumnVisibilityDropdown
                selectedVisits={selectedVisits}
                onVisitSelection={handleVisitSelection}
                onShowAllVisits={handleShowAllVisits}
              />
            </div>

            <FormStatusFilters
              filterStatus={filterStatus}
              onFilterClick={handleFilterClick}
            />
          </div>

          <ScheduleGrid
            periodConfig={periodConfig}
            filteredForms={filteredForms}
          />
        </div>
      </div>
    </Card>
  );
};
